// Types for service layer responses

export interface CustomerPointsSummary {
  total_earned: number;
  total_active: number;
  total_redeemed: number;
}

export interface CustomerTransaction {
  id: number;
  type: 'purchase' | 'redeem';
  business: string;
  category: string;
  points: number;
  amount: string;
  date: string;
}

export interface CustomerBusinessSummary {
  id: number;
  name: string;
  category: string;
  points: number;
  lastVisit: string;
}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}
